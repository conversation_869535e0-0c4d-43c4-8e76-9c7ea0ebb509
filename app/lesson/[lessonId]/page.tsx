import { redirect } from "next/navigation";

import { Quiz } from "../quiz";

type LessonIdPageProps = {
  params: {
    lessonId: number;
  };
};

// Mock data for lessons and challenges
const mockLessons = {
  1: {
    id: 1,
    title: "Grounding Basics",
    challenges: [
      {
        id: 1,
        order: 1,
        lessonId: 1,
        question: "What is the primary purpose of grounding meditation?",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 1, text: "To connect with the earth's energy", imageSrc: null, challengeId: 1, correct: true, audioSrc: null },
          { id: 2, text: "To lose weight", imageSrc: null, challengeId: 1, correct: false, audioSrc: null },
          { id: 3, text: "To sleep better", imageSrc: null, challengeId: 1, correct: false, audioSrc: null },
          { id: 4, text: "To become famous", imageSrc: null, challengeId: 1, correct: false, audioSrc: null },
        ],
      },
      {
        id: 2,
        order: 2,
        lessonId: 1,
        question: "Which of these is a grounding technique?",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 5, text: "Walking barefoot on grass", imageSrc: null, challengeId: 2, correct: true, audioSrc: null },
          { id: 6, text: "Watching TV", imageSrc: null, challengeId: 2, correct: false, audioSrc: null },
          { id: 7, text: "Eating fast food", imageSrc: null, challengeId: 2, correct: false, audioSrc: null },
          { id: 8, text: "Staying indoors all day", imageSrc: null, challengeId: 2, correct: false, audioSrc: null },
        ],
      },
      {
        id: 3,
        order: 3,
        lessonId: 1,
        question: "Grounding helps you feel more ___",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 9, text: "centered", imageSrc: null, challengeId: 3, correct: true, audioSrc: null },
          { id: 10, text: "anxious", imageSrc: null, challengeId: 3, correct: false, audioSrc: null },
          { id: 11, text: "confused", imageSrc: null, challengeId: 3, correct: false, audioSrc: null },
          { id: 12, text: "distracted", imageSrc: null, challengeId: 3, correct: false, audioSrc: null },
        ],
      },
    ],
  },
  2: {
    id: 2,
    title: "Earth Connection",
    challenges: [
      {
        id: 4,
        order: 1,
        lessonId: 2,
        question: "What element represents stability and grounding?",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 13, text: "Earth", imageSrc: null, challengeId: 4, correct: true, audioSrc: null },
          { id: 14, text: "Fire", imageSrc: null, challengeId: 4, correct: false, audioSrc: null },
          { id: 15, text: "Water", imageSrc: null, challengeId: 4, correct: false, audioSrc: null },
          { id: 16, text: "Air", imageSrc: null, challengeId: 4, correct: false, audioSrc: null },
        ],
      },
      {
        id: 5,
        order: 2,
        lessonId: 2,
        question: "The earth element is associated with ___",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 17, text: "stability", imageSrc: null, challengeId: 5, correct: true, audioSrc: null },
          { id: 18, text: "chaos", imageSrc: null, challengeId: 5, correct: false, audioSrc: null },
          { id: 19, text: "confusion", imageSrc: null, challengeId: 5, correct: false, audioSrc: null },
          { id: 20, text: "restlessness", imageSrc: null, challengeId: 5, correct: false, audioSrc: null },
        ],
      },
    ],
  },
  3: {
    id: 3,
    title: "Emotional Flow",
    challenges: [
      {
        id: 6,
        order: 1,
        lessonId: 3,
        question: "Water meditation helps with:",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 21, text: "Emotional healing", imageSrc: null, challengeId: 6, correct: true, audioSrc: null },
          { id: 22, text: "Being angry", imageSrc: null, challengeId: 6, correct: false, audioSrc: null },
          { id: 23, text: "Holding grudges", imageSrc: null, challengeId: 6, correct: false, audioSrc: null },
          { id: 24, text: "Avoiding feelings", imageSrc: null, challengeId: 6, correct: false, audioSrc: null },
        ],
      },
      {
        id: 7,
        order: 2,
        lessonId: 3,
        question: "Emotions are like water - they ___",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 25, text: "flow", imageSrc: null, challengeId: 7, correct: true, audioSrc: null },
          { id: 26, text: "freeze", imageSrc: null, challengeId: 7, correct: false, audioSrc: null },
          { id: 27, text: "disappear", imageSrc: null, challengeId: 7, correct: false, audioSrc: null },
          { id: 28, text: "explode", imageSrc: null, challengeId: 7, correct: false, audioSrc: null },
        ],
      },
    ],
  },
  4: {
    id: 4,
    title: "Inner Fire",
    challenges: [
      {
        id: 8,
        order: 1,
        lessonId: 4,
        question: "Fire element meditation helps with:",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 29, text: "Building inner strength", imageSrc: null, challengeId: 8, correct: true, audioSrc: null },
          { id: 30, text: "Feeling lazy", imageSrc: null, challengeId: 8, correct: false, audioSrc: null },
          { id: 31, text: "Giving up", imageSrc: null, challengeId: 8, correct: false, audioSrc: null },
          { id: 32, text: "Avoiding challenges", imageSrc: null, challengeId: 8, correct: false, audioSrc: null },
        ],
      },
      {
        id: 9,
        order: 2,
        lessonId: 4,
        question: "Fire represents ___ energy",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 33, text: "transformative", imageSrc: null, challengeId: 9, correct: true, audioSrc: null },
          { id: 34, text: "destructive", imageSrc: null, challengeId: 9, correct: false, audioSrc: null },
          { id: 35, text: "cold", imageSrc: null, challengeId: 9, correct: false, audioSrc: null },
          { id: 36, text: "stagnant", imageSrc: null, challengeId: 9, correct: false, audioSrc: null },
        ],
      },
    ],
  },
  5: {
    id: 5,
    title: "Breath Awareness",
    challenges: [
      {
        id: 10,
        order: 1,
        lessonId: 5,
        question: "Breathwork is associated with which element?",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 37, text: "Air", imageSrc: null, challengeId: 10, correct: true, audioSrc: null },
          { id: 38, text: "Earth", imageSrc: null, challengeId: 10, correct: false, audioSrc: null },
          { id: 39, text: "Fire", imageSrc: null, challengeId: 10, correct: false, audioSrc: null },
          { id: 40, text: "Water", imageSrc: null, challengeId: 10, correct: false, audioSrc: null },
        ],
      },
      {
        id: 11,
        order: 2,
        lessonId: 5,
        question: "Deep breathing helps you feel more ___",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 41, text: "calm", imageSrc: null, challengeId: 11, correct: true, audioSrc: null },
          { id: 42, text: "anxious", imageSrc: null, challengeId: 11, correct: false, audioSrc: null },
          { id: 43, text: "worried", imageSrc: null, challengeId: 11, correct: false, audioSrc: null },
          { id: 44, text: "stressed", imageSrc: null, challengeId: 11, correct: false, audioSrc: null },
        ],
      },
    ],
  },
  6: {
    id: 6,
    title: "Luna AI Introduction",
    challenges: [
      {
        id: 12,
        order: 1,
        lessonId: 6,
        question: "What is Luna AI's primary role?",
        type: "SELECT" as const,
        completed: false,
        challengeOptions: [
          { id: 45, text: "Wellness guidance companion", imageSrc: null, challengeId: 12, correct: true, audioSrc: null },
          { id: 46, text: "Shopping assistant", imageSrc: null, challengeId: 12, correct: false, audioSrc: null },
          { id: 47, text: "Game character", imageSrc: null, challengeId: 12, correct: false, audioSrc: null },
          { id: 48, text: "Weather reporter", imageSrc: null, challengeId: 12, correct: false, audioSrc: null },
        ],
      },
      {
        id: 13,
        order: 2,
        lessonId: 6,
        question: "Luna AI helps you on your ___ journey",
        type: "ASSIST" as const,
        completed: false,
        challengeOptions: [
          { id: 49, text: "wellness", imageSrc: null, challengeId: 13, correct: true, audioSrc: null },
          { id: 50, text: "shopping", imageSrc: null, challengeId: 13, correct: false, audioSrc: null },
          { id: 51, text: "gaming", imageSrc: null, challengeId: 13, correct: false, audioSrc: null },
          { id: 52, text: "cooking", imageSrc: null, challengeId: 13, correct: false, audioSrc: null },
        ],
      },
    ],
  },
};

const mockUserSubscription = {
  id: 1,
  userId: "mock-user-id",
  stripeCustomerId: "mock-stripe-customer-id",
  stripeSubscriptionId: "mock-stripe-subscription-id",
  stripePriceId: "mock-stripe-price-id",
  stripeCurrentPeriodEnd: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
  isActive: false,
};

const LessonIdPage = ({ params }: LessonIdPageProps) => {
  const lessonId = Number(params.lessonId);
  const lesson = mockLessons[lessonId as keyof typeof mockLessons];

  if (!lesson) redirect("/learn");

  const initialPercentage =
    (lesson.challenges.filter((challenge) => challenge.completed).length /
      lesson.challenges.length) *
    100;

  return (
    <Quiz
      initialLessonId={lesson.id}
      initialLessonChallenges={lesson.challenges}
      initialHearts={5}
      initialPercentage={initialPercentage}
      userSubscription={mockUserSubscription}
    />
  );
};

export default LessonIdPage;
