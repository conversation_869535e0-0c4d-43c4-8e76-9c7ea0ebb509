import { relations } from "drizzle-orm/relations";
import { lessons, challenges, challengeOptions, challengeProgress, courses, userProgress, units } from "./schema";

export const challengesRelations = relations(challenges, ({one, many}) => ({
	lesson: one(lessons, {
		fields: [challenges.lessonId],
		references: [lessons.id]
	}),
	challengeOptions: many(challengeOptions),
	challengeProgresses: many(challengeProgress),
}));

export const lessonsRelations = relations(lessons, ({one, many}) => ({
	challenges: many(challenges),
	unit: one(units, {
		fields: [lessons.unitId],
		references: [units.id]
	}),
}));

export const challengeOptionsRelations = relations(challengeOptions, ({one}) => ({
	challenge: one(challenges, {
		fields: [challengeOptions.challengeId],
		references: [challenges.id]
	}),
}));

export const challengeProgressRelations = relations(challengeProgress, ({one}) => ({
	challenge: one(challenges, {
		fields: [challengeProgress.challengeId],
		references: [challenges.id]
	}),
}));

export const userProgressRelations = relations(userProgress, ({one}) => ({
	course: one(courses, {
		fields: [userProgress.activeCourseId],
		references: [courses.id]
	}),
}));

export const coursesRelations = relations(courses, ({many}) => ({
	userProgresses: many(userProgress),
	units: many(units),
}));

export const unitsRelations = relations(units, ({one, many}) => ({
	course: one(courses, {
		fields: [units.courseId],
		references: [courses.id]
	}),
	lessons: many(lessons),
}));